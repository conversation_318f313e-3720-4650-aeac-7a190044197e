// Load environment variables
require('dotenv').config();

const { User, UserProfile, School, sequelize } = require('../src/models');
const logger = require('../src/utils/logger');

/**
 * Script untuk migrasi data school_origin ke school_id
 * Akan mencoba mencocokkan school_origin dengan nama sekolah di tabel schools
 */
async function migrateSchoolData() {
  try {
    console.log('🔄 Starting school data migration...\n');

    // Check database connection
    await sequelize.authenticate();
    console.log('✅ Database connection successful');

    // Get all user profiles with school_origin but no school_id
    console.log('\n📊 Analyzing current data...');
    const profilesWithSchoolOrigin = await UserProfile.findAll({
      where: {
        school_origin: {
          [sequelize.Sequelize.Op.ne]: null
        },
        school_id: null
      },
      attributes: ['user_id', 'school_origin']
    });

    console.log(`Found ${profilesWithSchoolOrigin.length} profiles with school_origin but no school_id`);

    if (profilesWithSchoolOrigin.length === 0) {
      console.log('✅ No migration needed - all profiles already have school_id or no school_origin');
      return;
    }

    // Get unique school origins
    const uniqueSchoolOrigins = [...new Set(profilesWithSchoolOrigin.map(p => p.school_origin))];
    console.log(`Found ${uniqueSchoolOrigins.length} unique school origins`);

    // Try to match with existing schools
    console.log('\n🔍 Matching school origins with existing schools...');
    const matchResults = [];
    let matchedCount = 0;
    let unmatchedCount = 0;

    for (const schoolOrigin of uniqueSchoolOrigins) {
      // Try exact match first
      let school = await School.findOne({
        where: {
          name: schoolOrigin
        }
      });

      // Try case-insensitive match
      if (!school) {
        school = await School.findOne({
          where: sequelize.where(
            sequelize.fn('LOWER', sequelize.col('name')),
            'LIKE',
            `%${schoolOrigin.toLowerCase()}%`
          )
        });
      }

      if (school) {
        matchResults.push({
          schoolOrigin,
          schoolId: school.id,
          schoolName: school.name,
          matched: true
        });
        matchedCount++;
        console.log(`✅ Matched: "${schoolOrigin}" -> "${school.name}" (ID: ${school.id})`);
      } else {
        matchResults.push({
          schoolOrigin,
          schoolId: null,
          schoolName: null,
          matched: false
        });
        unmatchedCount++;
        console.log(`❌ No match: "${schoolOrigin}"`);
      }
    }

    console.log(`\n📈 Match results: ${matchedCount} matched, ${unmatchedCount} unmatched`);

    // Ask for confirmation before proceeding
    if (matchedCount > 0) {
      console.log('\n⚠️  This will update user profiles with matched school_id values.');
      console.log('Do you want to proceed? (This is a dry run - set DRY_RUN=false to actually update)');
      
      const isDryRun = process.env.DRY_RUN !== 'false';
      
      if (isDryRun) {
        console.log('\n🔍 DRY RUN MODE - No actual updates will be made');
      } else {
        console.log('\n💾 LIVE MODE - Updates will be applied to database');
      }

      // Update profiles with matched schools
      const transaction = await sequelize.transaction();
      
      try {
        let updatedCount = 0;
        
        for (const match of matchResults) {
          if (match.matched) {
            const profilesToUpdate = profilesWithSchoolOrigin.filter(
              p => p.school_origin === match.schoolOrigin
            );
            
            for (const profile of profilesToUpdate) {
              if (!isDryRun) {
                await UserProfile.update(
                  { school_id: match.schoolId },
                  { 
                    where: { user_id: profile.user_id },
                    transaction 
                  }
                );
              }
              updatedCount++;
              console.log(`${isDryRun ? '🔍' : '✅'} ${isDryRun ? 'Would update' : 'Updated'} profile ${profile.user_id}: school_id = ${match.schoolId}`);
            }
          }
        }

        if (!isDryRun) {
          await transaction.commit();
          console.log(`\n✅ Migration completed! Updated ${updatedCount} profiles`);
        } else {
          await transaction.rollback();
          console.log(`\n🔍 Dry run completed! Would update ${updatedCount} profiles`);
          console.log('To apply changes, run: DRY_RUN=false node scripts/migrate-school-data.js');
        }

      } catch (error) {
        await transaction.rollback();
        throw error;
      }
    }

    // Report unmatched schools
    if (unmatchedCount > 0) {
      console.log('\n📋 Unmatched school origins (consider adding these to schools table):');
      matchResults
        .filter(m => !m.matched)
        .forEach(m => {
          const count = profilesWithSchoolOrigin.filter(p => p.school_origin === m.schoolOrigin).length;
          console.log(`   - "${m.schoolOrigin}" (${count} profiles)`);
        });
    }

    console.log('\n🎉 School data migration analysis completed!');

  } catch (error) {
    console.error('❌ Migration failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run the migration
if (require.main === module) {
  migrateSchoolData();
}

module.exports = migrateSchoolData;
