/**
 * Migration Script: Unify Users and Admins Tables
 * 
 * This script migrates data from the separate 'admins' table into the unified 'users' table
 * and adds the necessary columns to support both user types.
 */

const { Sequelize, QueryTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
const path = require('path');

// Load environment variables from .env file
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Debug environment variables
console.log('🔍 Environment Variables:');
console.log(`   DB_HOST: ${process.env.DB_HOST}`);
console.log(`   DB_PORT: ${process.env.DB_PORT}`);
console.log(`   DB_NAME: ${process.env.DB_NAME}`);
console.log(`   DB_USER: ${process.env.DB_USER}`);
console.log(`   DB_SCHEMA: ${process.env.DB_SCHEMA}`);
console.log(`   Password length: ${process.env.DB_PASSWORD ? process.env.DB_PASSWORD.length : 'undefined'}`);

// Database configuration with fallbacks and validation
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  database: process.env.DB_NAME || 'atma_db',
  username: process.env.DB_USER || 'atma_user',
  password: process.env.DB_PASSWORD || 'secret-passworrd',
  dialect: 'postgres',
  schema: process.env.DB_SCHEMA || 'auth',
  logging: console.log
};

console.log('\n🔧 Database Configuration:');
console.log(`   Host: ${dbConfig.host}:${dbConfig.port}`);
console.log(`   Database: ${dbConfig.database}`);
console.log(`   Username: ${dbConfig.username}`);
console.log(`   Schema: ${dbConfig.schema}`);

const sequelize = new Sequelize(dbConfig);

async function migrateUnifyUsersAdmins() {
  console.log('🚀 Starting Users-Admins Unification Migration...\n');

  try {
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established successfully');

    // Step 1: Add new columns to users table
    console.log('\n📝 Step 1: Adding new columns to users table...');
    
    try {
      await sequelize.query(`
        ALTER TABLE auth.users 
        ADD COLUMN IF NOT EXISTS username VARCHAR(100) UNIQUE,
        ADD COLUMN IF NOT EXISTS user_type VARCHAR(20) NOT NULL DEFAULT 'user',
        ADD COLUMN IF NOT EXISTS is_active BOOLEAN NOT NULL DEFAULT true,
        ADD COLUMN IF NOT EXISTS last_login TIMESTAMP WITH TIME ZONE;
      `);
      console.log('✅ New columns added successfully');
    } catch (error) {
      console.log('⚠️  Columns might already exist:', error.message);
    }

    // Step 2: Add constraints and indexes
    console.log('\n📝 Step 2: Adding constraints and indexes...');
    
    try {
      await sequelize.query(`
        ALTER TABLE auth.users 
        ADD CONSTRAINT chk_user_type 
        CHECK (user_type IN ('user', 'admin', 'superadmin', 'moderator'));
      `);
      console.log('✅ User type constraint added');
    } catch (error) {
      console.log('⚠️  Constraint might already exist:', error.message);
    }

    try {
      await sequelize.query(`
        CREATE UNIQUE INDEX IF NOT EXISTS idx_users_username_unique 
        ON auth.users (username) 
        WHERE username IS NOT NULL;
      `);
      console.log('✅ Username unique index added');
    } catch (error) {
      console.log('⚠️  Index might already exist:', error.message);
    }

    try {
      await sequelize.query(`
        CREATE INDEX IF NOT EXISTS idx_users_user_type ON auth.users (user_type);
        CREATE INDEX IF NOT EXISTS idx_users_is_active ON auth.users (is_active);
        CREATE INDEX IF NOT EXISTS idx_users_admin_lookup ON auth.users (user_type, is_active, email);
      `);
      console.log('✅ Additional indexes added');
    } catch (error) {
      console.log('⚠️  Indexes might already exist:', error.message);
    }

    // Step 3: Check if admins table exists and has data
    console.log('\n📝 Step 3: Checking admins table...');
    
    const adminTableExists = await sequelize.query(`
      SELECT EXISTS (
        SELECT FROM information_schema.tables 
        WHERE table_schema = 'auth' 
        AND table_name = 'admins'
      );
    `, { type: QueryTypes.SELECT });

    if (!adminTableExists[0].exists) {
      console.log('⚠️  Admins table does not exist. Skipping migration.');
      return;
    }

    const adminsData = await sequelize.query(`
      SELECT * FROM auth.admins ORDER BY created_at;
    `, { type: QueryTypes.SELECT });

    console.log(`📊 Found ${adminsData.length} admin records to migrate`);

    if (adminsData.length === 0) {
      console.log('⚠️  No admin data to migrate.');
    } else {
      // Step 4: Migrate admin data to users table
      console.log('\n📝 Step 4: Migrating admin data...');
      
      const transaction = await sequelize.transaction();
      
      try {
        let migratedCount = 0;
        let skippedCount = 0;

        for (const admin of adminsData) {
          // Check if user with same email already exists
          const existingUser = await sequelize.query(`
            SELECT id FROM auth.users WHERE email = :email;
          `, {
            replacements: { email: admin.email },
            type: QueryTypes.SELECT,
            transaction
          });

          if (existingUser.length > 0) {
            console.log(`⚠️  Skipping admin ${admin.email} - user already exists`);
            skippedCount++;
            continue;
          }

          // Check if username already exists (if admin has username)
          if (admin.username) {
            const existingUsername = await sequelize.query(`
              SELECT id FROM auth.users WHERE username = :username;
            `, {
              replacements: { username: admin.username },
              type: QueryTypes.SELECT,
              transaction
            });

            if (existingUsername.length > 0) {
              console.log(`⚠️  Username ${admin.username} already exists, will use email as identifier`);
              admin.username = null;
            }
          }

          // Insert admin as user
          await sequelize.query(`
            INSERT INTO auth.users (
              id, username, email, password_hash, user_type, 
              is_active, token_balance, last_login, created_at, updated_at
            ) VALUES (
              :id, :username, :email, :password_hash, :user_type,
              :is_active, NULL, :last_login, :created_at, :updated_at
            );
          `, {
            replacements: {
              id: admin.id,
              username: admin.username,
              email: admin.email,
              password_hash: admin.password_hash,
              user_type: admin.role, // Map role to user_type
              is_active: admin.is_active,
              last_login: admin.last_login,
              created_at: admin.created_at,
              updated_at: admin.updated_at
            },
            transaction
          });

          // Create user profile for admin if they have full_name
          if (admin.full_name) {
            await sequelize.query(`
              INSERT INTO auth.user_profiles (
                user_id, full_name, created_at, updated_at
              ) VALUES (
                :user_id, :full_name, :created_at, :updated_at
              ) ON CONFLICT (user_id) DO UPDATE SET
                full_name = EXCLUDED.full_name,
                updated_at = EXCLUDED.updated_at;
            `, {
              replacements: {
                user_id: admin.id,
                full_name: admin.full_name,
                created_at: admin.created_at,
                updated_at: admin.updated_at
              },
              transaction
            });
          }

          migratedCount++;
          console.log(`✅ Migrated admin: ${admin.email} (${admin.role})`);
        }

        await transaction.commit();
        console.log(`\n✅ Migration completed successfully!`);
        console.log(`   - Migrated: ${migratedCount} admins`);
        console.log(`   - Skipped: ${skippedCount} admins`);

      } catch (error) {
        await transaction.rollback();
        throw error;
      }
    }

    // Step 5: Verify migration
    console.log('\n📝 Step 5: Verifying migration...');
    
    const userStats = await sequelize.query(`
      SELECT 
        user_type,
        COUNT(*) as count,
        COUNT(CASE WHEN is_active THEN 1 END) as active_count
      FROM auth.users 
      GROUP BY user_type 
      ORDER BY user_type;
    `, { type: QueryTypes.SELECT });

    console.log('\n📊 User Statistics After Migration:');
    userStats.forEach(stat => {
      console.log(`   ${stat.user_type}: ${stat.count} total (${stat.active_count} active)`);
    });

    // Step 6: Create backup and cleanup instructions
    console.log('\n📝 Step 6: Cleanup Instructions');
    console.log('⚠️  IMPORTANT: Before dropping the admins table, please:');
    console.log('   1. Verify all admin functionality works with the unified users table');
    console.log('   2. Run comprehensive tests');
    console.log('   3. Create a backup of the admins table:');
    console.log('      CREATE TABLE auth.admins_backup AS SELECT * FROM auth.admins;');
    console.log('   4. Only then drop the admins table:');
    console.log('      DROP TABLE auth.admins;');

    console.log('\n🎉 Migration completed successfully!');

  } catch (error) {
    console.error('❌ Migration failed:', error);
    throw error;
  } finally {
    await sequelize.close();
  }
}

// Run migration if called directly
if (require.main === module) {
  migrateUnifyUsersAdmins()
    .then(() => {
      console.log('\n✅ Migration script completed');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Migration script failed:', error);
      process.exit(1);
    });
}

module.exports = { migrateUnifyUsersAdmins };
