// Load environment variables
require('dotenv').config();

const { User, UserProfile, School, sequelize } = require('../src/models');
const logger = require('../src/utils/logger');

/**
 * Test script untuk memverifikasi integrasi school_id dengan tabel schools
 */
async function testSchoolIntegration() {
  try {
    console.log('🔍 Testing School Integration...\n');

    // Test 1: Verify models are properly loaded
    console.log('1. Testing model loading...');
    console.log('✅ User model loaded:', !!User);
    console.log('✅ UserProfile model loaded:', !!UserProfile);
    console.log('✅ School model loaded:', !!School);
    console.log('✅ Sequelize instance loaded:', !!sequelize);

    // Test 2: Check database connection
    console.log('\n2. Testing database connection...');
    await sequelize.authenticate();
    console.log('✅ Database connection successful');

    // Test 3: Check if tables exist
    console.log('\n3. Checking table existence...');
    const [usersResult] = await sequelize.query("SELECT to_regclass('auth.users') as exists");
    const [profilesResult] = await sequelize.query("SELECT to_regclass('auth.user_profiles') as exists");
    const [schoolsResult] = await sequelize.query("SELECT to_regclass('public.schools') as exists");
    
    console.log('✅ auth.users table exists:', !!usersResult[0].exists);
    console.log('✅ auth.user_profiles table exists:', !!profilesResult[0].exists);
    console.log('✅ public.schools table exists:', !!schoolsResult[0].exists);

    // Test 4: Check if school_id column exists in user_profiles
    console.log('\n4. Checking user_profiles schema...');
    const [columnsResult] = await sequelize.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_schema = 'auth' 
      AND table_name = 'user_profiles'
      ORDER BY ordinal_position
    `);
    
    console.log('📋 user_profiles columns:');
    columnsResult.forEach(col => {
      console.log(`   - ${col.column_name}: ${col.data_type} (nullable: ${col.is_nullable})`);
    });

    const hasSchoolId = columnsResult.some(col => col.column_name === 'school_id');
    console.log(hasSchoolId ? '✅ school_id column exists' : '❌ school_id column missing');

    // Test 5: Test creating a school (or use existing school)
    console.log('\n5. Testing school creation...');
    let testSchool;
    try {
      testSchool = await School.create({
        name: 'Test School Integration',
        address: 'Test Address',
        city: 'Test City',
        province: 'Test Province'
      });
      console.log('✅ School created:', testSchool.id, testSchool.name);
    } catch (error) {
      if (error.message.includes('permission denied')) {
        console.log('⚠️  Cannot create school (permission denied), using existing school...');
        // Try to find an existing school
        testSchool = await School.findOne();
        if (testSchool) {
          console.log('✅ Using existing school:', testSchool.id, testSchool.name);
        } else {
          console.log('❌ No existing schools found, skipping write tests');
          console.log('🎉 Read-only tests completed successfully!');
          return;
        }
      } else {
        throw error;
      }
    }

    // Test 6: Test creating user with profile and school_id
    console.log('\n6. Testing user creation with school_id...');
    let testUser, testProfile;
    let createdTestData = false;

    try {
      testUser = await User.create({
        email: '<EMAIL>',
        password_hash: 'test_hash',
        username: 'testschooluser'
      });
      console.log('✅ User created:', testUser.id, testUser.email);

      testProfile = await UserProfile.create({
        user_id: testUser.id,
        full_name: 'Test User School Integration',
        school_id: testSchool.id,
        school_origin: 'Test School Origin',
        date_of_birth: '1995-01-01',
        gender: 'male'
      });
      console.log('✅ Profile created with school_id:', testProfile.user_id, testProfile.school_id);
      createdTestData = true;
    } catch (error) {
      if (error.message.includes('permission denied')) {
        console.log('⚠️  Cannot create test data (permission denied), using existing data...');
        // Try to find existing user with profile
        testUser = await User.findOne({
          include: [{
            model: UserProfile,
            as: 'profile',
            where: { school_id: { [sequelize.Sequelize.Op.ne]: null } },
            required: true
          }]
        });
        if (testUser && testUser.profile) {
          testProfile = testUser.profile;
          console.log('✅ Using existing user with profile:', testUser.id, testProfile.school_id);
        } else {
          console.log('❌ No existing user with school_id found, skipping association tests');
          console.log('🎉 Basic tests completed successfully!');
          return;
        }
      } else {
        throw error;
      }
    }

    // Test 7: Test associations
    console.log('\n7. Testing associations...');
    const userWithProfile = await User.findByPk(testUser.id, {
      include: [{
        model: UserProfile,
        as: 'profile',
        include: [{
          model: School,
          as: 'school'
        }]
      }]
    });

    console.log('✅ User with profile and school loaded');
    console.log('   - User:', userWithProfile.email);
    console.log('   - Profile:', userWithProfile.profile?.full_name);
    console.log('   - School:', userWithProfile.profile?.school?.name);

    // Test 8: Test UserProfile.findBySchoolId method
    console.log('\n8. Testing UserProfile.findBySchoolId method...');
    const profilesBySchool = await UserProfile.findBySchoolId(testSchool.id);
    console.log('✅ Found profiles by school_id:', profilesBySchool.count);

    // Test 9: Test school distribution
    console.log('\n9. Testing school distribution...');
    const distribution = await UserProfile.getSchoolDistribution();
    console.log('✅ School distribution retrieved:', distribution.length, 'schools');

    // Cleanup (only if we created test data)
    if (createdTestData) {
      console.log('\n🧹 Cleaning up test data...');
      try {
        await testProfile.destroy();
        await testUser.destroy();
        // Only delete school if we created it
        if (testSchool.name === 'Test School Integration') {
          await testSchool.destroy();
        }
        console.log('✅ Test data cleaned up');
      } catch (error) {
        console.log('⚠️  Cleanup failed (this is okay):', error.message);
      }
    } else {
      console.log('\n✅ No cleanup needed (used existing data)');
    }

    console.log('\n🎉 All tests passed! School integration is working correctly.');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('Stack trace:', error.stack);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

// Run the test
if (require.main === module) {
  testSchoolIntegration();
}

module.exports = testSchoolIntegration;
