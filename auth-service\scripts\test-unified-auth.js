/**
 * Test Unified Authentication System
 *
 * This script tests the unified authentication system after migration
 */

const { Sequelize, QueryTypes } = require('sequelize');
const bcrypt = require('bcryptjs');
const path = require('path');

// Load environment variables from .env file
require('dotenv').config({ path: path.join(__dirname, '../.env') });

// Debug environment variables
console.log('🔍 Environment Variables:');
console.log(`   DB_HOST: ${process.env.DB_HOST}`);
console.log(`   DB_PORT: ${process.env.DB_PORT}`);
console.log(`   DB_NAME: ${process.env.DB_NAME}`);
console.log(`   DB_USER: ${process.env.DB_USER}`);
console.log(`   DB_SCHEMA: ${process.env.DB_SCHEMA}`);
console.log(`   Password length: ${process.env.DB_PASSWORD ? process.env.DB_PASSWORD.length : 'undefined'}`);

// Database configuration with fallbacks and validation
const dbConfig = {
  host: process.env.DB_HOST || 'localhost',
  port: parseInt(process.env.DB_PORT) || 5432,
  database: process.env.DB_NAME || 'atma_db',
  username: process.env.DB_USER || 'atma_user',
  password: process.env.DB_PASSWORD || 'secret-passworrd',
  dialect: 'postgres',
  schema: process.env.DB_SCHEMA || 'auth',
  logging: false // Disable logging for cleaner output
};

console.log('\n🔧 Database Configuration:');
console.log(`   Host: ${dbConfig.host}:${dbConfig.port}`);
console.log(`   Database: ${dbConfig.database}`);
console.log(`   Username: ${dbConfig.username}`);
console.log(`   Schema: ${dbConfig.schema}`);

const sequelize = new Sequelize(dbConfig);

async function testUnifiedAuth() {
  console.log('🧪 Testing Unified Authentication System...\n');

  try {
    // Test database connection
    await sequelize.authenticate();
    console.log('✅ Database connection established');

    // Test 1: Check unified users table structure
    console.log('\n📝 Test 1: Checking unified users table structure...');
    const tableInfo = await sequelize.query(`
      SELECT column_name, data_type, is_nullable, column_default
      FROM information_schema.columns 
      WHERE table_schema = 'auth' AND table_name = 'users'
      ORDER BY ordinal_position;
    `, { type: QueryTypes.SELECT });

    const expectedColumns = [
      'id', 'username', 'email', 'password_hash', 'user_type', 
      'is_active', 'token_balance', 'last_login', 'created_at', 'updated_at'
    ];

    const actualColumns = tableInfo.map(col => col.column_name);
    const missingColumns = expectedColumns.filter(col => !actualColumns.includes(col));
    
    if (missingColumns.length === 0) {
      console.log('✅ All required columns present in users table');
    } else {
      console.log('❌ Missing columns:', missingColumns);
    }

    // Test 2: Check user types distribution
    console.log('\n📝 Test 2: Checking user types distribution...');
    const userTypes = await sequelize.query(`
      SELECT 
        user_type,
        COUNT(*) as count,
        COUNT(CASE WHEN is_active THEN 1 END) as active_count
      FROM auth.users 
      GROUP BY user_type 
      ORDER BY user_type;
    `, { type: QueryTypes.SELECT });

    console.log('📊 User Types Distribution:');
    userTypes.forEach(type => {
      console.log(`   ${type.user_type}: ${type.count} total (${type.active_count} active)`);
    });

    // Test 3: Test admin login functionality
    console.log('\n📝 Test 3: Testing admin login functionality...');
    const adminUsers = await sequelize.query(`
      SELECT id, username, email, user_type, is_active
      FROM auth.users 
      WHERE user_type IN ('admin', 'superadmin', 'moderator')
      AND is_active = true
      LIMIT 3;
    `, { type: QueryTypes.SELECT });

    if (adminUsers.length > 0) {
      console.log('✅ Found admin users:');
      adminUsers.forEach(admin => {
        console.log(`   - ${admin.username || admin.email} (${admin.user_type})`);
      });
    } else {
      console.log('⚠️  No active admin users found');
    }

    // Test 4: Test regular user functionality
    console.log('\n📝 Test 4: Testing regular user functionality...');
    const regularUsers = await sequelize.query(`
      SELECT id, email, user_type, token_balance, is_active
      FROM auth.users 
      WHERE user_type = 'user'
      AND is_active = true
      LIMIT 3;
    `, { type: QueryTypes.SELECT });

    if (regularUsers.length > 0) {
      console.log('✅ Found regular users:');
      regularUsers.forEach(user => {
        console.log(`   - ${user.email} (balance: ${user.token_balance})`);
      });
    } else {
      console.log('⚠️  No active regular users found');
    }

    // Test 5: Check user profiles relationship
    console.log('\n📝 Test 5: Testing user profiles relationship...');
    const profilesCount = await sequelize.query(`
      SELECT 
        COUNT(*) as total_profiles,
        COUNT(CASE WHEN u.user_type = 'user' THEN 1 END) as user_profiles,
        COUNT(CASE WHEN u.user_type IN ('admin', 'superadmin', 'moderator') THEN 1 END) as admin_profiles
      FROM auth.user_profiles up
      JOIN auth.users u ON up.user_id = u.id;
    `, { type: QueryTypes.SELECT });

    const profile = profilesCount[0];
    console.log(`✅ User profiles: ${profile.total_profiles} total`);
    console.log(`   - Regular users: ${profile.user_profiles}`);
    console.log(`   - Admin users: ${profile.admin_profiles}`);

    // Test 6: Check indexes
    console.log('\n📝 Test 6: Checking database indexes...');
    const indexes = await sequelize.query(`
      SELECT 
        indexname,
        indexdef
      FROM pg_indexes 
      WHERE schemaname = 'auth' AND tablename = 'users'
      ORDER BY indexname;
    `, { type: QueryTypes.SELECT });

    console.log('📋 Database indexes:');
    indexes.forEach(idx => {
      console.log(`   - ${idx.indexname}`);
    });

    // Test 7: Performance test
    console.log('\n📝 Test 7: Performance test - user lookup...');
    const startTime = Date.now();
    
    await sequelize.query(`
      SELECT u.id, u.email, u.user_type, up.full_name
      FROM auth.users u
      LEFT JOIN auth.user_profiles up ON u.id = up.user_id
      WHERE u.is_active = true
      LIMIT 100;
    `, { type: QueryTypes.SELECT });
    
    const endTime = Date.now();
    console.log(`✅ Query completed in ${endTime - startTime}ms`);

    // Test 8: Check constraints
    console.log('\n📝 Test 8: Checking database constraints...');
    const constraints = await sequelize.query(`
      SELECT 
        conname as constraint_name,
        contype as constraint_type
      FROM pg_constraint 
      WHERE conrelid = 'auth.users'::regclass
      ORDER BY conname;
    `, { type: QueryTypes.SELECT });

    console.log('🔒 Database constraints:');
    constraints.forEach(constraint => {
      const type = {
        'p': 'Primary Key',
        'u': 'Unique',
        'c': 'Check',
        'f': 'Foreign Key'
      }[constraint.constraint_type] || constraint.constraint_type;
      
      console.log(`   - ${constraint.constraint_name} (${type})`);
    });

    console.log('\n🎉 All tests completed successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ Database structure is correct');
    console.log('✅ User types are properly distributed');
    console.log('✅ Admin and user functionality is ready');
    console.log('✅ User profiles relationship is working');
    console.log('✅ Database indexes are in place');
    console.log('✅ Performance is acceptable');
    console.log('✅ Database constraints are active');

  } catch (error) {
    console.error('❌ Test failed:', error.message);
    console.error('\n🔧 Please check:');
    console.error('1. Database connection settings');
    console.error('2. Migration has been run successfully');
    console.error('3. Required tables exist');
    throw error;
  } finally {
    await sequelize.close();
  }
}

// Run tests if called directly
if (require.main === module) {
  testUnifiedAuth()
    .then(() => {
      console.log('\n✅ Test script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n❌ Test script failed:', error);
      process.exit(1);
    });
}

module.exports = { testUnifiedAuth };
